<main class="mt-5">
  <div class="container-fluid">
    <div class="mb-4">
      <!-- Heading row -->
      <div class="row mb-4">
        <div class="col-12">
          <h3 class="mb-0">{{ isCarRental ? "Hirers List" : "Clients" }}</h3>
        </div>
      </div>

      <!-- Search and button row -->
      <div class="row align-items-center">
        <div class="col-12 col-md-4 mb-3 mb-md-0">
          <input
            type="text"
            class="form-control"
            [placeholder]="
              isCarRental ? 'Search hirers...' : 'Search clients...'
            "
            [(ngModel)]="searchInput"
            (change)="setSearchInput($event)"
            (keyup.enter)="applyFilter()"
          />
        </div>
        <div class="col-12 col-md-4 mb-3 mb-md-0">
          <button class="btn btn-primary" (click)="applyFilter()">
            <i class="fa fa-search"></i> Search
          </button>
          <button class="btn btn-secondary ml-2" (click)="clearFilters()">
            <i class="fa fa-times"></i> Clear
          </button>
        </div>

        @if (!isCarRental) {
        <div class="col-12 col-md-4">
          @if (userType == "agency" ) {
          <button
            type="button"
            class="btn btn-success"
            (click)="openAddClientModal()"
            title="Add a new client or link with existing client."
          >
            <i class="fa fa-plus-circle"></i>
            <span class="ml-2">{{
              isCarRental ? "Add Hirer" : "Add Client"
            }}</span>
          </button>
          }
        </div>
        }
      </div>
    </div>
  </div>

  <!-- Content -->
  <section class="content mt-4">
    <div class="container-fluid">
      <table class="table table-striped mb-0 styled-table text-left">
        <thead>
          <tr class="text-left">
            <th (click)="sort('name')" class="pointer">
              Name
              <app-sort-icon
                [sortBy]="sortBy()"
                column="name"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th>Rating</th>
            <th>Bookings</th>
            <th>Verification</th>
            @if (!isCarRental) {
            <th (click)="sort('service')" class="pointer">
              Service
              <app-sort-icon
                [sortBy]="sortBy()"
                column="service"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            }
            <th (click)="sort('telephone')" class="pointer">
              Phone
              <app-sort-icon
                [sortBy]="sortBy()"
                column="telephone"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th (click)="sort('email')" class="pointer">
              Email
              <app-sort-icon
                [sortBy]="sortBy()"
                column="email"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th>Actions</th>
            @if (!isCarRental) {
            <th (click)="sort('purchaseOrder')" class="pointer">
              Purchase Order #
              <app-sort-icon
                [sortBy]="sortBy()"
                column="purchaseOrder"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            } @if (!isCarRental) {
            <th (click)="sort('sbsCode')" class="pointer">
              SBS Code
              <app-sort-icon
                [sortBy]="sortBy()"
                column="sbsCode"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            } @if (!isCarRental) {
            <th>Actions</th>
            }
          </tr>
        </thead>
        <tbody>
          @for (c of clients(); track c.id) {
          <tr>
            <td class="text-primary-custom">{{ c?.name || "-" }}</td>
            <td class="text-primary-custom">
              {{ c?.rating ? c.rating.toFixed(2) : "-" }}
            </td>
            <td>{{ c?.totalBooking || "-" }}</td>
            <td>
              <i
                class="bi bi-patch-check-fill fa-lg mr-2"
                [ngClass]="{
                    'text-success': c?.verified,
                    'text-secondary': !c?.verified,
                  }"
              ></i>
              <span>{{ c?.verified ? "Verified" : "Unverified" }}</span>
            </td>

            @if (!isCarRental) {
            <td>{{ c.service }}</td>
            }
            <td>{{ c?.telephone || "-" }}</td>
            <td>{{ c?.email || "-" }}</td>
            <td>
              <!-- [routerLink]="['/agency/clients', c.id]" -->
              <a
                (click)="navigateToClient(c.id)"
                class="text-primary-custom pointer"
                title="View Client Details"
              >
                <i class="fa fa-eye"></i>
              </a>
            </td>

            @if (!isCarRental) {
            <td>{{ c.purchaseOrder }}</td>
            } @if (!isCarRental) {
            <td>{{ c.sbsCode }}</td>
            } @if (userType != "agency" && !isCarRental) {
            <td>
              @if (userType == "client") {
              <span class="mr-3 text-primary-custom pointer">
                <i class="fa fa-eye" title="view"></i>
              </span>
              } @if (userType == "admin") {
              <span
                class="mr-3 text-success pointer"
                title="Edit Agency Details"
                (click)="openModal(edit)"
              >
                <i class="fa fa-pencil-alt" title="edit"></i>
              </span>
              } @if (userType == "admin") {
              <span
                class="text-primary-custom pointer"
                title="Manage Users"
                (click)="openBigModal(users, c.id)"
              >
                <i class="fa fa-users-cog"></i>
              </span>
              }
            </td>
            } @if (userType == "agency" && !isCarRental) {
            <td>
              @if ("UPDATE_CLIENT" | permission) {
              <span
                class="mr-3 text-success pointer"
                (click)="loadClient(c.id, edit, true)"
                title="View or edit client info"
              >
                <i class="fa fa-pencil-alt" title="edit"></i>
              </span>
              }
              <!-- <span class="text-primary-custom pointer" title="Manage Users" (click)="openBigModal(users, c.id)">
                <i class="fa fa-users-cog"></i>
              </span> -->
            </td>
            }
          </tr>
          }
        </tbody>
      </table>
      @if (clients()) {
      <section class="pagination-container">
        <div class="container-fluid">
          <div class="row m-0">
            <div
              class="col-12 col-sm-12 col-md-5 text-left acontent-center d-flex"
            >
              <span class="acontent-center pr-2">
                Showing {{ pageNumber() * pageSize() + 1 }} - {{ showin() }} of
                {{ totalItems() }}
              </span>

              <select
                style="width: min-content; justify-self: right"
                class="form-control"
                [ngModel]="pageSize()"
                (ngModelChange)="
                  pageSize.set($event); resetPageNumber(); getClients()
                "
              >
                @for (size of [10, 20, 25, 50, 100]; track size) {
                <option [value]="size">{{ size }}</option>
                }
              </select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 text-right">
              <button
                class="btn text-white"
                (click)="handlePageChange('prev')"
                [disabled]="first()"
              >
                <i class="fa fa-caret-up"></i>&nbsp;&nbsp;Previous Page
              </button>
            </div>
            <div class="col-12 col-sm-6 col-md-3 text-right pr-0">
              <button
                class="btn text-white"
                (click)="handlePageChange('next')"
                [disabled]="last()"
              >
                Next Page&nbsp;&nbsp;<i class="fa fa-caret-down"></i>
              </button>
            </div>
          </div>
        </div>
      </section>
      }
    </div>
  </section>
</main>

<!-- Modals -->
<ng-template class="modal fade" #add let-modal>
  <div class="">
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center w-100">Add New Client</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <form [formGroup]="addForm" (ngSubmit)="newClient(addForm, $event)">
        <div class="bg-modal card-body">
          <div class="container-fluid">
            <section>
              <div class="col-12">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">About Client</span>
                <hr class="bg-black mt-0" />
              </div>
              <div class="row">
                <!-- <div class="form-group col-12 col-sm-12 col-md-6">
                  <input
                    type="text"
                    formControlName="name"
                    placeholder="Client Name"
                    class="form-control"
                  />
                </div> -->
                <div class="form-group col-12 col-sm-12 col-md-12">
                  <select
                    id="clientList"
                    (change)="loadClientInfo($event)"
                    class="form-control"
                  >
                    <option value="" selected disabled>
                      Select Other Client
                    </option>
                    <option value="NEW">New Client</option>
                    @for (a of allClients(); track a.id) {
                    <option value="{{ a.id }}">
                      {{ a.name }}
                    </option>
                    }
                  </select>
                </div>
              </div>
              <fieldset>
                <div class="row">
                  <div class="form-group col-12 col-sm-12 col-md-6">
                    <input
                      type="text"
                      formControlName="name"
                      placeholder="Client Name"
                      class="form-control"
                    />
                  </div>
                  <div class="form-group col-12 col-sm-12 col-md-6">
                    <select formControlName="serviceId" class="form-control">
                      <option value="" selected disabled>
                        Select Services
                      </option>
                      @for (s of services(); track s.id) {
                      <option value="{{ s.id }}">
                        {{ s.name }}
                      </option>
                      }
                    </select>
                  </div>
                </div>
                <div class="col-12 mt-4">
                  <i class="bi bi-info-circle"></i>
                  <span class="ml-2">Contacts and Addresses</span>
                  <hr class="bg-black mt-0" />
                </div>
                <div formGroupName="address">
                  <div class="row">
                    <div class="form-group col-12 col-sm-12 col-md-12">
                      <input
                        type="text"
                        formControlName="firstLine"
                        placeholder="First Line Address"
                        class="form-control"
                      />
                    </div>
                  </div>
                  <div class="row">
                    <div class="form-group col-12 col-sm-12 col-md-12">
                      <input
                        type="text"
                        formControlName="town"
                        placeholder="Town"
                        class="form-control"
                      />
                    </div>
                  </div>
                  <div class="row">
                    <div class="form-group col-12 col-sm-12 col-md-12">
                      <input
                        type="text"
                        formControlName="postcode"
                        placeholder="Post Code"
                        class="form-control"
                      />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="text"
                      formControlName="telephone"
                      placeholder="Telephone"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="email"
                      (keyup)="checkEmail($event)"
                      formControlName="email"
                      placeholder="Email"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-12 mt-4" [hidden]="existing">
                  <i class="bi bi-info-circle"></i>
                  <span class="ml-2">Admin</span>
                  <hr class="bg-black mt-0" />
                </div>
                <div
                  class="row"
                  formGroupName="administratorCreateDto"
                  [hidden]="existing"
                >
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="email"
                      formControlName="adminEmail"
                      (keyup)="checkEmail($event)"
                      placeholder="Admin Email"
                      class="form-control"
                    />
                  </div>
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="text"
                      formControlName="firstname"
                      placeholder="Admin First Name"
                      class="form-control"
                    />
                  </div>
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="text"
                      formControlName="lastname"
                      placeholder="Admin Last Name"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-12 mt-4">
                  <i class="bi bi-info-circle"></i>
                  <span class="ml-2">Billing</span>
                  <hr class="bg-black mt-0" />
                </div>
                <div class="row">
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="email"
                      formControlName="billingEmail"
                      (keyup)="checkEmail($event)"
                      placeholder="Billing Email"
                      class="form-control"
                    />
                  </div>
                </div>
                <!-- Purchase Order number -->
                <div class="col-12 mt-4">
                  <i class="bi bi-info-circle"></i>
                  <span class="ml-2">Purchase Order Number</span>
                  <hr class="bg-black mt-0" />
                </div>

                <div class="row">
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="text"
                      formControlName="purchaseOrder" 
                      placeholder="Purchase order number"
                      class="form-control" 
                    />
                  </div>
                </div>
                <!-- Ends here -->
                <div class="col-12 mt-4">
                  <i class="bi bi-info-circle"></i>
                  <span class="ml-2">SBS Code</span>
                  <hr class="bg-black mt-0" />
                </div>

                <div class="row">
                  <div class="form-group col-12 col-sm-12 col-md-12">
                    <input
                      type="text"
                      formControlName="sbsCode" 
                      placeholder="SBS Code"
                      class="form-control"
                    />
                  </div>
                </div>

                <!-- Ends here -->
              </fieldset>
            </section>
          </div>
        </div>
        <div align="right" class="mt-3">
          <button
            type="button"
            class="btn btn-danger btn-sm mr-3"
            (click)="modal.dismiss()"
          >
            Cancel
          </button>
          @if (new) {
          <button
            type="button"
            class="btn btn-sm btn-success"
            [disabled]="isCreatingClient()"
          >
            @if (isCreatingClient()) {
              <i class="fa fa-spinner fa-spin"></i> Creating...
            } @else {
              Add New Client
            }
          </button>
          } @if (existing) {
          <button
            type="button"
            (click)="createLinkToAgent()"
            class="btn btn-sm btn-success"
          >
            Add Client Link
          </button>
          }
        </div>
      </form>
    </div>
  </div>
</ng-template>

<ng-template class="modal fade" #edit let-modal>
  <div class="">
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center w-100">View Client Information</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <form [formGroup]="editForm" (ngSubmit)="updateClient(editForm)">
        <div class="bg-modal card-body">
          <div class="container-fluid">
            <section>
              <div class="col-12">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">About Client</span>
                <hr class="bg-black mt-0" />
              </div>
              <div class="row">
                <div class="form-group col-12 col-sm-12 col-md-12">
                  <input
                    type="text"
                    formControlName="name"
                    placeholder="Client Name"
                    class="form-control"
                    disabled
                  />
                </div>
              </div>
              <div class="row">
                <!-- <div class="form-group col-12 col-sm-12 col-md-6">
                  <select formControlName="status" class="form-control">
                    <option value="" selected disabled>Select Status</option>
                    <option value="ACTIVE">Active</option>
                    <option value="INACTIVE">INACTIVE</option>
                  </select>
                </div> -->
                <div class="form-group col-12 col-sm-12 col-md-12">
                  <select formControlName="serviceId" class="form-control">
                    <option value="" selected disabled>Select Services</option>
                    @for (s of services(); track s.id) {
                    <option value="{{ s.id }}">
                      {{ s.name }}
                    </option>
                    }
                  </select>
                </div>
              </div>
              <div class="col-12 mt-4">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">Contacts and Addresses</span>
                <hr class="bg-black mt-0" />
              </div>
              <div formGroupName="address" class="row">
                <div class="form-group col-12 col-sm-12 col-md-6">
                  <input
                    type="text"
                    formControlName="firstLine"
                    placeholder="First Line Address"
                    class="form-control"
                  />
                </div>
                <div class="form-group col-12 col-sm-12 col-md-6">
                  <input
                    type="text"
                    formControlName="town"
                    placeholder="Town"
                    class="form-control"
                  />
                </div>
                <div class="form-group col-12 col-sm-12 col-md-6">
                  <input
                    type="text"
                    formControlName="postcode"
                    placeholder="Post Code"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="row">
                <div class="form-group col-12 col-sm-12 col-md-6">
                  <input
                    type="text"
                    formControlName="telephone"
                    placeholder="Telephone"
                    class="form-control"
                  />
                </div>
                <div class="form-group col-12 col-sm-12 col-md-6">
                  <input
                    type="email"
                    (keyup)="checkEmail($event)"
                    formControlName="email"
                    placeholder="Email"
                    class="form-control"
                    disabled
                  />
                </div>
              </div>

              <div class="col-12 mt-4">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">Billing</span>
                <hr class="bg-black mt-0" />
              </div>
              <div class="row">
                <div class="form-group col-12 col-sm-12 col-md-12">
                  <input
                    type="email"
                    formControlName="billingEmail"
                    (keyup)="checkEmail($event)"
                    placeholder="Billing Email"
                    class="form-control"
                  />
                </div>
              </div>

              <!-- Puchase order starts here  -->
              <div class="col-12 mt-4">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">Purchase order Number</span>
                <hr class="bg-black mt-0" />
              </div>

              <div class="row">
                <div class="form-group col-12 col-sm-12 col-md-12">
                  <input
                    type="email"
                    formControlName="purchaseOrder"
                    placeholder="Purchase order Number"
                    class="form-control"
                  />
                </div>
              </div>

              <!-- ENds here  -->

              <div class="col-12 mt-4">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">SBS Code</span>
                <hr class="bg-black mt-0" />
              </div>

              <div class="row">
                <div class="form-group col-12 col-sm-12 col-md-12">
                  <input
                    type="email"
                    formControlName="sbsCode"
                    placeholder="Billing Email"
                    class="form-control"
                  />
                </div>
              </div>

              <!-- Sbs code starts here   -->
            </section>
          </div>
        </div>
        <div align="right" class="mt-3">
          <button
            type="button"
            class="btn btn-danger btn-sm mr-3"
            (click)="modal.dismiss()"
          >
            Cancel
          </button>
          <button type="button" class="btn btn-sm btn-success">
            Update Client
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>

<ng-template class="modal fade" #delete let-modal>
  <div class="">
    <div class="modal-header bg-danger text-white">
      <h5 class="modal-title text-center w-100">Delete CLient</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <div class="bg-modal card-body">
        <div class="text-center">
          <h3>You are about to delete this Client?</h3>
        </div>
      </div>
      <div align="right" class="mt-3">
        <button class="btn btn-info btn-sm mr-3" (click)="modal.dismiss()">
          Cancel
        </button>
        <button class="btn btn-sm btn-danger" (click)="deleteClient()">
          Delete CLient
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template class="modal fade" #users let-modal>
  <div class="">
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center w-100">View Client Users</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <table class="table table-striped mb-0 styled-table text-center">
        <thead>
          <tr class="text-center">
            <th>Name</th>
            <!-- <th>Username</th> -->
            <th>Email</th>
            <!-- <th>Phone</th> -->
            <th>Role</th>
            <th>Status</th>
          </tr>
        </thead>
        @if (userList()) {
        <tbody>
          @for (u of userList(); track u.id) {
          <tr>
            <td>{{ u.firstname }}</td>
            <!-- <td>{{u.username}} </td> -->
            <td>{{ u.email }}</td>
            <td>{{ getName(u.roles[0].name) }}</td>
            <!-- <td></td> -->
            <td>
              @if (u.enabled == "Active") {
              <span>
                <i class="fa fa-square text-success"></i>
              </span>
              } @if (u.enabled == "Inactive") {
              <span>
                <i class="fa fa-square text-danger-custom"></i>
              </span>
              }
            </td>
          </tr>
          }
        </tbody>
        }
      </table>
    </div>
  </div>
</ng-template>
