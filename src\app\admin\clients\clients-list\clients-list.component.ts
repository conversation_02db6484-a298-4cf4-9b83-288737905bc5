import { AuthService } from './../../../shared/services/auth.service';
import { AgencyService } from './../../../shared/services/agency.service';
import { ServicesService } from './../../../shared/services/services.service';
import { Component, OnInit, computed, effect, resource, signal, ViewChild, TemplateRef } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { ClientService } from 'src/app/shared/services/client.service';
import { UsersService } from 'src/app/shared/services/users.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { environment } from 'src/environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { lastValueFrom } from 'rxjs';
declare var $: any;

interface Page<T> {
    content: T[];
    totalElements: number;
    totalPages: number;
    last: boolean;
    first: boolean;
    size: number;
    number: number;
}

@Component({
    selector: 'app-clients-list',
    templateUrl: './clients-list.component.html',
    styleUrls: ['./clients-list.component.css'],
    standalone: false
})
export class ClientsListComponent implements OnInit {
    @ViewChild('add') addModal: TemplateRef<any>;

    // Search filter as signal
    searchQuery = signal('ac');
    // Input field value (doesn't trigger search automatically)
    searchInput = '';

    // UI state as signals
    allClients = signal<any[]>([]);
    userList = signal<any[]>([]);
    agencies = signal<any[]>([]);
    services = signal<any[]>([]);
    selectedClient = signal<any>(null);
    clientId = signal<number | null>(null);
    showUsers = signal(false);
    submitted = signal(false);

    // Pagination state as signals
    pageNumber = signal(0);
    pageSize = signal(25);
    sortBy = signal('name');
    directionAsc = signal(true);

    // Forms
    addForm: UntypedFormGroup;
    editForm: UntypedFormGroup;

    // Computed resource parameters
    resourceParams = computed(() => ({
        page: this.pageNumber(),
        size: this.pageSize(),
        sort: this.sortBy(),
        direction: this.directionAsc(),
        searchQuery: this.searchQuery()
    }));

    // Define the type for our resource parameters
    private resourceParamsType = {
        page: 0,
        size: 25,
        sort: 'name',
        direction: true,
        searchQuery: ''
    };

    navigateToClient(clientId: number): void {
        // Navigate to the client details page
        this.router.navigate(['/agency/clients', clientId]);
    }

    // Resource for clients
    clientsResource = resource<Page<any>, typeof this.resourceParamsType>({
        request: () => ({
            page: this.pageNumber(),
            size: this.pageSize(),
            sort: this.sortBy(),
            direction: this.directionAsc(),
            searchQuery: this.searchQuery()
        }),
        loader: async ({ request }) => {

            return await lastValueFrom(this.clientService.getPaginatedClients(
                request.page,
                request.size,
                request.searchQuery,
                request.sort,
                request.direction,
                this.userType == 'admin' ? null : this.storageService.decrypt(localStorage.getItem('agentId'))

            ));

        }
    });

    // Computed properties for UI
    clients = computed(() => {
        return this.clientsResource.value()?.content || [];
    });

    totalItems = computed(() => this.clientsResource.value()?.totalElements || 0);
    first = computed(() => this.clientsResource.value()?.first || false);
    last = computed(() => this.clientsResource.value()?.last || false);
    showin = computed(() => {
        const total = this.totalItems();
        const page = this.pageNumber();
        const size = this.pageSize();
        return (page + 1) * size > total ? total : (page + 1) * size;
    });

    userType: string;
    new: boolean = true;
    existing: boolean;
    formArea: boolean;
    agencyId: any;
    isCarRental = environment.isCarRental;

    constructor(
        private toast: ToastrService,
        private modalService: NgbModal,
        private fb: UntypedFormBuilder,
        private clientService: ClientService,
        private servicesService: ServicesService,
        private agencyService: AgencyService,
        private authService: AuthService,
        private usersService: UsersService,
        private storageService: StorageService,
        private router: Router,
        private route: ActivatedRoute
    ) {
        this.userType = authService.getUserType();
        this.agencyId = this.storageService.decrypt(localStorage.getItem('agentId'));

        // Initialize sortBy with a default value
        this.sortBy.set('name');

        // Set up effect to update URL when any state changes
        effect(() => {
            // This will run whenever pagination, sorting, or search query changes
            // Note: searchQuery is included to update URL when search is applied/cleared
            console.log('State changed, updating URL');
            console.log('Page:', this.pageNumber());
            console.log('Sort:', this.sortBy(), this.directionAsc());
            console.log('Search:', this.searchQuery());

            // Update URL with current state
            this.updateUrlWithState();
        });
    }

    ngOnInit(): void {
        // Initialize forms
        this.formInit();
        this.editForm = this.fb.group({
            id: 0
        });

        // Get services and agencies for dropdowns
        this.getServices();
        this.getAgencies();

        // Read URL parameters and set initial state
        this.route.queryParams.subscribe(params => {
            if (params['page']) this.pageNumber.set(Number(params['page']));
            if (params['size']) this.pageSize.set(Number(params['size']));
            if (params['sortBy']) this.sortBy.set(params['sortBy']);
            if (params['directionAsc']) this.directionAsc.set(params['directionAsc'] === 'true');
            if (params['searchQuery']) {
                this.searchQuery.set(params['searchQuery']); // Also set the input field
            }
        });

        // Load initial data
        this.getClients();
    }

    // Update URL with current state
    updateUrlWithState(): void {
        // Build query params object with current state
        const queryParams: any = {
            page: this.pageNumber(),
            size: this.pageSize(),
            sortBy: this.sortBy(),
            directionAsc: this.directionAsc().toString()
        };

        // Add filter values if they exist
        if (this.searchQuery()) {
            queryParams.searchQuery = this.searchQuery();
        }

        // Update URL without reloading the page
        this.router.navigate([], {
            relativeTo: this.route,
            queryParams: queryParams,
            queryParamsHandling: '', // Don't merge - replace all query params to remove empty ones
            replaceUrl: true // Replace the current URL to avoid adding to browser history
        });
    }

    formInit() {
        this.addForm = this.fb.group({
            name: ['', Validators.required],
            logo: [""],
            email: ['', Validators.required],
            // status: ['ACTIVE', Validators.required],
            serviceId: ['', Validators.required],
            agencyId: [this.storageService.decrypt(localStorage.getItem('agentId'))],
            address: this.fb.group({
                firstLine: [''],
                postcode: [''],
                town: [''],
            }),
            telephone: [''],
            administratorCreateDto: this.fb.group({
                adminEmail: ['', Validators.required],
                firstname: [''],
                lastname: [''],
            }),
            billingEmail: ['', Validators.required],
            purchaseOrder: [''],
            sbsCode: ['']
        });

        if (this.addModal) {
            this.openModal(this.addModal);
        }
    }

    handlePageChange(event: string) {
        if (event === 'next' && !this.last()) {
            this.pageNumber.set(this.pageNumber() + 1);
        } else if (event === 'prev' && this.pageNumber() > 0) {
            this.pageNumber.set(this.pageNumber() - 1);
        }

        this.getClients();
    }

    resetPageNumber(): void {
        this.pageNumber.set(0);
    }

    getClients() {
        // Refresh the resource to load clients with current filters
        this.clientsResource.reload();
    }

    setSearchInput(event) {
        this.searchInput = event.target.value;
        console.log(this.searchInput);
    }

    applyFilter(): void {
        var searchInp = this.searchInput;
        // Copy the input value to the actual search query
        this.searchQuery.set(this.searchInput);
        this.resetPageNumber();
        this.getClients();
    }

    clearFilters(): void {
        this.searchQuery.set('');
        this.searchInput = ''; // Also clear the input field
        this.resetPageNumber();
        this.getClients();
    }

    sort(field: string): void {
        // Reset to first page when sorting
        this.pageNumber.set(0);

        if (this.sortBy() === field) {
            // Toggle sorting direction
            this.directionAsc.set(!this.directionAsc());
        } else {
            // Set new column to sort by
            this.sortBy.set(field);
            this.directionAsc.set(true);
        }

        // Reload clients with new sorting
        this.getClients();
    }

    getAllClients() {
        this.clientService.getPaginatedClients(0, 100).subscribe(
            data => {
                this.allClients.set(data.content);
            }
        );
    }

    searchClient() {
        // This method is now replaced by the applyFilter method
        // The search is handled automatically through the resource
        this.applyFilter();
    }

    getServices() {
        this.servicesService.getPaginatedServices(0, 100).subscribe(
            data => {
                // console.table(data.content);
                this.services.set(data.content);
            }
        );
    }

    getAgencies() {
        this.agencyService.getPaginatedAgencies(0, 100).subscribe(
            data => {
                this.agencies.set(data.content);
            }
        );
    }


    getServiceId(ref) {
        let sid;
        if (ref) {
            this.services().forEach(r => {
                if (r.name == ref) {
                    sid = r.id;
                }
            });
        }

        return sid;
    }


    openModal(modal) {
        this.modalService.open(modal, { centered: true, size: 'lg' });
    }

    newClient(form: UntypedFormGroup, event?: Event) {
        if (event) event.preventDefault();
        if (form.valid) {
            this.clientService.createClient(form.value).subscribe(
                resp => {
                    this.modalService.dismissAll();
                    this.getClients();
                    this.toast.success('Client Added Successfully');
                }
            );
        } else {
            // Collect missing/invalid fields
            const missingFields: string[] = [];
            const controls = form.controls;
            if (controls['name']?.invalid) missingFields.push('Name');
            if (controls['email']?.invalid) missingFields.push('Email');
            if (controls['serviceId']?.invalid) missingFields.push('Service');
            if (controls['agencyId']?.invalid) missingFields.push('Agency');
            if (controls['billingEmail']?.invalid) missingFields.push('Billing Email');
            // Address group
            if (controls['address']?.invalid) {
                const addressGroup = controls['address'] as UntypedFormGroup;
                if (addressGroup.controls['firstLine']?.invalid) missingFields.push('Address First Line');
                if (addressGroup.controls['postcode']?.invalid) missingFields.push('Address Postcode');
                if (addressGroup.controls['town']?.invalid) missingFields.push('Address Town');
            }
            // Telephone
            if (controls['telephone']?.invalid) missingFields.push('Telephone');
            // Administrator group
            if (controls['administratorCreateDto']?.invalid) {
                const adminGroup = controls['administratorCreateDto'] as UntypedFormGroup;
                if (adminGroup.controls['adminEmail']?.invalid) missingFields.push('Admin Email');
                if (adminGroup.controls['firstname']?.invalid) missingFields.push('Admin First Name');
                if (adminGroup.controls['lastname']?.invalid) missingFields.push('Admin Last Name');
            }
            // Purchase code and sbs code (if required)
            if (controls['purchaseCode']?.invalid) missingFields.push('Purchase Code');
            if (controls['sbsCode']?.invalid) missingFields.push('SBS Code');

            if (missingFields.length > 0) {
                this.toast.warning('Missing Required Info: ' + missingFields.join(', '));
            } else {
                this.toast.warning('Missing Required Info.');
            }
        }
    }


    updateClient(form: UntypedFormGroup) {
        console.table(form.value);

        if (form.valid) {
            this.clientService.updateClient(form.value).subscribe(
                resp => {
                    // console.table(resp);
                    this.modalService.dismissAll();
                    this.getClients();
                    this.toast.success('Client Updated Successfully');
                }
            );
        }
        else {
            this.toast.warning('Missing Required Info.');
        }
    }


    loadClient(ref, modal, edit) {
        if (ref) {
            const client = this.clients().find(r => r.id == ref);
            if (client) {
                this.selectedClient.set(client);
            }

            if (edit) {
                const selectedClient = this.selectedClient();
                this.editForm = this.fb.group({
                    id: [selectedClient.id],
                    name: [selectedClient.name, Validators.required],
                    email: [selectedClient.email, Validators.required],
                    billingEmail: [selectedClient.billingEmail, Validators.required],
                    purchaseOrder: [selectedClient.purchaseOrder],
                    sbsCode: [selectedClient.sbsCode],
                    // status: [selectedClient.status, Validators.required],
                    serviceId: [this.getServiceId(selectedClient.service), Validators.required],
                    address: this.fb.group({
                        firstLine: [selectedClient.firstLine],
                        postcode: [selectedClient.postCode],
                        town: [selectedClient?.town],
                    }),
                    telephone: [selectedClient.telephone, Validators.required],
                });

                this.editForm.patchValue(selectedClient);
            }

            this.openModal(modal);
        }
    }

    validateEmail(email) {
        const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(email);
    }

    checkEmail(ele) {
        // console.log(ele);

        var typed = ele.target.value;

        if (this.validateEmail(typed)) {
            $(ele.target).removeClass('is-invalid');
            $(ele.target).addClass('is-valid');
        }
        else {
            $(ele.target).removeClass('is-valid');
            $(ele.target).addClass('is-invalid');
        }
    }

    deleteClient() {
        const selectedClient = this.selectedClient();
        if (selectedClient?.id) {
            this.clientService.deleteClient(selectedClient.id).subscribe(
                resp => {
                    // console.table(resp);
                    this.modalService.dismissAll();
                    this.getClients();
                    this.toast.success('Client Deleted Successfully');
                }
            );
        }
        else {
            this.toast.warning('Missing Required Info');
        }
    }

    getUsers() {
        this.userList.set([]);
        this.usersService.getPaginatedClientUsers(0, 100, this.clientId()).subscribe(
            data => {
                console.log(data.content);

                this.userList.set(data.content);
                this.showUsers.set(true);
            }
        );
    }

    openBigModal(modal, ref) {
        console.log(ref);
        this.clientId.set(ref);
        this.getUsers();
        this.modalService.open(modal, { centered: true, size: 'xl' });
    }

    getName(name) {
        if (name) {
            let n = name.replace('ROLE', "");
            return n.replace(/_/g, " ");
        }

        return '';
    }

    activateUser(ref, role, enable) {

        let formData: UntypedFormGroup = this.fb.group({
            userId: ref,
            roleName: role,
            enabled: enable
        });

        this.usersService.enableUser(formData.value).subscribe(
            resp => {
                // console.log(resp);
                this.toast.success('User Status Changed.')
                this.getUsers();
            }
        );

    }

    loadClientInfo(ele) {
        let ref = ele.target.value;

        if (ref) {
            let r;

            this.allClients().forEach(a => {
                if (a.id == ref) {
                    r = a;
                    this.clientId.set(a.id);
                }
            });

            if (ref != 'NEW') {
                this.addForm = this.fb.group({
                    name: [r.name, Validators.required],
                    logo: [r.logo],
                    email: [r.email, Validators.required],
                    // status: ['ACTIVE', Validators.required],
                    serviceId: [this.getServiceId(r.service), Validators.required],
                    agencyId: [this.storageService.decrypt(localStorage.getItem('agentId'))],
                    address: this.fb.group({
                        firstLine: [r.firstLine, Validators.required],
                        postcode: [r.postCode, Validators.required],
                        town: ['', Validators.required],
                    }),
                    telephone: [r.telephone, Validators.required],
                    administratorCreateDto: this.fb.group({
                        adminEmail: ['', Validators.required],
                        firstname: ['', Validators.required],
                        lastname: ['', Validators.required],
                    }),
                    billingEmail: [r.billingEmail, Validators.required],
                    purchaseCode: [r.purchaseCode],
                    sbsCode: [r.sbsCode]
                });

                this.getAdminInfo(r.id);

                this.new = false;
                this.existing = true;
                this.formArea = true;
            }
            else {
                this.addForm = this.fb.group({
                    name: ['', Validators.required],
                    logo: [''],
                    email: ['', Validators.required],
                    // status: ['ACTIVE', Validators.required],
                    serviceId: ['', Validators.required],
                    agencyId: [this.storageService.decrypt(localStorage.getItem('agentId'))],
                    address: this.fb.group({
                        firstLine: ['', Validators.required],
                        postcode: ['', Validators.required],
                        town: ['', Validators.required],
                    }),
                    telephone: ['', Validators.required],
                    administratorCreateDto: this.fb.group({
                        adminEmail: ['', Validators.required],
                        firstname: ['', Validators.required],
                        lastname: ['', Validators.required],
                    }),
                    billingEmail: ['', Validators.required],
                    purchaseCode: ['', Validators.required],
                    sbsCode: ['', Validators.required]
                });

                this.new = true;
                this.existing = false;
                this.formArea = false;
            }
        }
    }

    getAdminInfo(ref) {
        // console.log(ref);

    }

    createLinkToAgent() {
        // console.log(this.clientId());
        this.clientService.linkClientToAgent(this.clientId(), this.storageService.decrypt(localStorage.getItem('agentId'))).subscribe(
            resp => {
                console.table(resp);
                this.modalService.dismissAll();
                this.getClients();
                // this.addForm.reset();
                // this.addForm.value.agencyId = localStorage.getItem('agentId');
                this.toast.success('Client Linked Successfully');
            }
        );
    }


}
